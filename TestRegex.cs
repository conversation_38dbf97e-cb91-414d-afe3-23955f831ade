using System;
using System.Text.RegularExpressions;

class TestRegex
{
    static void Main()
    {
        // 模拟你的HTML数据
        string testHtml = @"<td class=""ant-table-cell""><!----><div style=""line-height: 22px;"">还空箱</div><div style=""line-height: 18px; color: rgba(11, 13, 14, 0.6); font-size: 12px;"">于 2025-05-30 15:03:00</div><!----></td>";
        
        Console.WriteLine("原始HTML:");
        Console.WriteLine(testHtml);
        Console.WriteLine();
        
        // 移除注释标记
        string td = testHtml.Replace("<!---->", "");
        Console.WriteLine("移除注释后:");
        Console.WriteLine(td);
        Console.WriteLine();
        
        if (td.Contains("div"))
        {
            string divPattern = @"<div[^>]*>(.*?)<\/div>";
            MatchCollection divMatches = Regex.Matches(td, divPattern, RegexOptions.Singleline);
            
            Console.WriteLine($"找到 {divMatches.Count} 个div匹配");
            for (int i = 0; i < divMatches.Count; i++)
            {
                Console.WriteLine($"Div {i}: '{divMatches[i].Groups[1].Value}'");
            }
            
            if (divMatches.Count == 2)
            {
                string statusText = divMatches[0].Groups[1].Value.Trim();
                string dateText = divMatches[1].Groups[1].Value.Trim();
                
                Console.WriteLine($"状态文本: '{statusText}'");
                Console.WriteLine($"日期文本: '{dateText}'");
                
                if (statusText == "还空箱" || statusText == "Empty Return")
                {
                    string returnDateString = dateText.Replace("At", "").Replace("于", "").Trim();
                    Console.WriteLine($"匹配到还空箱，处理后日期: '{returnDateString}'");
                }
            }
        }
    }
}
