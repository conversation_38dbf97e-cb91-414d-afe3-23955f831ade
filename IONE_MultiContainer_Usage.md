# IONE 多集装箱爬虫使用说明

## 问题描述

原始的IONE爬虫在处理多个集装箱时存在以下问题：
1. 点击集装箱号后，页面会刷新显示该集装箱的详细信息
2. 每次点击都会覆盖之前的信息，导致无法同时获取多个集装箱的信息
3. 需要一种机制来依次处理所有集装箱并收集完整信息

## 解决方案

### 1. 改进的处理流程

新的IONE爬虫采用以下步骤：

1. **跳转到搜索页面** (IProcessType.JUMPTO)
2. **点击跟踪标签** (IProcessType.OPERATE)
3. **输入搜索关键字** (IProcessType.OPERATE)
4. **点击搜索按钮** (IProcessType.OPERATE)
5. **批量处理所有集装箱** (IProcessType.OPERATE) - **新增步骤**
6. **解析所有集装箱数据** (IProcessType.READ)

### 2. 关键改进

#### JavaScript 批量处理逻辑

```javascript
async function processAllContainers(table) {
    window.allContainerData = [];
    
    // 收集所有集装箱链接
    const containerLinks = [];
    for(let i = 1; i < table.rows.length; i++) {
        const row = table.rows[i];
        const link = row.querySelector('a');
        if(link) {
            containerLinks.push({
                element: link,
                text: link.textContent.trim(),
                index: i
            });
        }
    }
    
    // 依次点击每个集装箱并收集详细信息
    for(let i = 0; i < containerLinks.length; i++) {
        const linkInfo = containerLinks[i];
        
        // 点击集装箱链接
        linkInfo.element.click();
        
        // 等待详细信息加载
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 收集当前集装箱的详细信息
        const containerDetail = {
            containerNo: linkInfo.text,
            detailHtml: iframeDocument.documentElement.innerHTML,
            index: i
        };
        
        window.allContainerData.push(containerDetail);
    }
}
```

#### C# 数据处理逻辑

```csharp
private IResult ProcessMultipleContainers(string jsonData)
{
    IResult result = new IResult();
    BindingList<IResultContainer> allContainers = new BindingList<IResultContainer>();
    
    // 解析JSON数据
    JArray containerArray = JArray.Parse(jsonData);
    
    foreach (JObject containerObj in containerArray)
    {
        string containerNo = containerObj["containerNo"]?.ToString() ?? "";
        string detailHtml = containerObj["detailHtml"]?.ToString() ?? "";
        
        if (!string.IsNullOrEmpty(detailHtml))
        {
            // 从每个集装箱的详细HTML中提取信息
            var containerInfo = ExtractContainerInfo(detailHtml, containerNo);
            if (containerInfo != null)
            {
                allContainers.Add(containerInfo);
            }
        }
    }
    
    result.ContainerList = allContainers;
    return result;
}
```

### 3. 使用方法

#### 基本使用

```csharp
// 创建IONE爬虫实例
IONE oneSpider = new IONE();

// 运行爬虫
List<IProcessItem> processItems = oneSpider.Run("YOUR_SEARCH_KEY", "");

// 处理步骤会自动执行，包括：
// 1. 导航到ONE网站
// 2. 输入搜索条件
// 3. 批量处理所有集装箱
// 4. 返回包含所有集装箱信息的结果
```

#### 结果处理

```csharp
// 在GetResult方法中，会自动检测数据格式
private IResult GetResult(string resultString)
{
    IResult result = new IResult();
    
    // 检查是否是JSON格式的多集装箱数据
    if (resultString.StartsWith("[") && resultString.Contains("containerNo"))
    {
        // 处理多集装箱JSON数据
        result = ProcessMultipleContainers(resultString);
    }
    else
    {
        // 处理单个集装箱的HTML数据（兼容原有逻辑）
        result.ETA = this.GetETA(resultString);
        result.ContainerList = this.GetContainerItems(resultString);
    }
    
    return result;
}
```

### 4. 优势

1. **完整性**: 能够获取所有集装箱的详细信息
2. **自动化**: 无需手动干预，自动处理多个集装箱
3. **兼容性**: 保持与原有框架的兼容性
4. **可靠性**: 包含错误处理和重试机制
5. **可扩展性**: 易于添加新的信息提取逻辑

### 5. 注意事项

1. **等待时间**: 每次点击集装箱后需要等待2秒让页面加载完成
2. **网络延迟**: 在网络较慢的环境下可能需要调整等待时间
3. **页面结构**: 如果ONE网站更新页面结构，可能需要调整提取逻辑
4. **错误处理**: 建议在生产环境中添加更详细的错误日志

### 6. 扩展建议

1. **动态等待**: 可以改进为检测页面加载状态而不是固定等待时间
2. **并行处理**: 在条件允许的情况下可以考虑并行处理多个集装箱
3. **缓存机制**: 对于重复查询可以添加缓存机制
4. **监控告警**: 添加处理失败的监控和告警机制

## 总结

这个改进的IONE爬虫解决了多集装箱信息收集的问题，通过JavaScript的异步处理和C#的JSON解析，实现了完整的多集装箱数据爬取功能。
