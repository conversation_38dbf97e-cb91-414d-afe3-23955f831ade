using HIH.Framework.AutoCrawingManager.Result;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager.Process
{
    public class IONE : IProcess
    {
        private readonly string SearchMainPage = "https://ecomm.one-line.com/one-ecom";

        public IONE():base("ONE","ONEY"){}


        public override List<IProcessItem> Run(string searchKey,string replaceText)
        {
            List<IProcessItem> processList = new List<IProcessItem>();

            IProcessItem firPro = new IProcessItem(0, "跳转", IProcessType.JUMPTO);
            IProcessItem secPro = new IProcessItem(1, "点击", IProcessType.OPERATE);
            IProcessItem thiPro = new IProcessItem(2, "输入", IProcessType.OPERATE);
            IProcessItem fouPro = new IProcessItem(3, "点击", IProcessType.OPERATE);
            IProcessItem fifPro = new IProcessItem(4, "点击", IProcessType.OPERATE);
            IProcessItem sixPro = new IProcessItem(5, "解析", IProcessType.READ, this.GetResult);

            searchKey = string.IsNullOrEmpty(replaceText) ? searchKey : searchKey.Replace(replaceText, "");
            firPro.JScript = this.SearchMainPage;
            secPro.JScript = "let trackTab = document.getElementById('quick-action_tracking-box');trackTab.click();";
            thiPro.JScript = "let input = document.querySelector('textarea');if(input){input.focus(); document.execCommand('insertText', false, '" + searchKey + "');}";
            fouPro.JScript = "var buttons = document.querySelectorAll('button');" +
                        "buttons.forEach(function(button) {" +
                        "button.classList.forEach(function(clazz){" +
                        "if(clazz.includes('TrackInputAreaContent_track-button')){" +
                        "button.click();" +
                        "return" +
                        "}" +
                        "})" +
                        "});";
            fifPro.JScript = @"(function() {
                        const iframe = document.getElementById('IframeCurrentEcom');
                        if(iframe){
                            const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
                            const table = iframeDocument.getElementById('main-grid');
                            if (table) {
                                // 使用 MutationObserver 监听表格的变化
                                const observer = new MutationObserver((mutationsList, observer) => {
                                    // 检查表格是否有足够的行
                                    if (table.rows.length >= 2) {
                                        observer.disconnect(); // 停止观察
                                        processTableRows(table);
                                    }
                                });
                                // 开始观察表格的变化
                                observer.observe(table, { childList: true, subtree: true });
                                // 立即检查表格是否有足够的行
                                if (table.rows.length >= 2) {
                                    observer.disconnect(); // 停止观察
                                    processTableRows(table);
                                }
                            }
                             function processTableRows(table) {
                                 var row = table.rows[1];
                                 let link = row.querySelector('a');
                                 console.log(link);
                                 link.click();
                             }
                        }
                    })()";

            sixPro.JScript = @"(function() {
                        var iframe = document.getElementById('IframeCurrentEcom');
                        if (iframe) {
                            const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
                            return iframeDocument.documentElement.innerHTML;
                        } else {
                            return '';
                        }
                    })()";

            processList.Add(firPro);
            processList.Add(secPro);
            processList.Add(thiPro);
            processList.Add(fouPro);
            processList.Add(fifPro);
            processList.Add(sixPro);

            return processList;
        }

        private IResult GetResult(string resultString)
        {
            try
            {
                IResult result = new IResult();
                try
                {
                    result.ETA = this.GetETA(resultString);
                }
                catch (Exception exc)
                {
                    result.ETAExc = exc.Message;
                }
                try
                {
                    result.ContainerList = this.GetContainerItems(resultString);
                }
                catch (Exception)
                {

                    throw;
                }

                return result;
            }
            catch (Exception exc)
            {
                throw;
            }
        }


        private string GetETA(string result)
        {
            try
            {
                string contentPattern = @"<table[^>]*id=""[^""]*sailing""[^>]*>(.*?)<\/table>";

                MatchCollection matches = Regex.Matches(result, contentPattern, RegexOptions.Singleline);

                if (matches.Count <= 0)
                    throw new Exception("未找到正确的匹配对象");

                string matchVal = matches[0].Value;

                string trPattern = @"<tr>(.*?)<\/tr>";

                MatchCollection trMatches = Regex.Matches(matchVal, trPattern, RegexOptions.Singleline);

                if (trMatches.Count < 2)
                    throw new Exception("未找到正确的匹配对象");

                matchVal = trMatches[trMatches.Count - 1].Value;

                string tdPattern = @"<td[^>]*>(.*?)<\/td>";

                MatchCollection tdMatches = Regex.Matches(matchVal, tdPattern, RegexOptions.Singleline);

                if (tdMatches.Count < 5)
                    throw new Exception("未找到正确的匹配对象");

                matchVal = tdMatches[4].Value;
                string etaPattern = @"<\/span>(.*?)<\/td>";

                MatchCollection etaMatches = Regex.Matches(matchVal, etaPattern, RegexOptions.Singleline);

                if (etaMatches.Count <= 0 || etaMatches[0].Groups.Count < 2)
                    throw new Exception("未找到正确的匹配对象");

                string etaValue = etaMatches[0].Groups[1].Value;

                return this.ParseExactTime(etaValue);

            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private string ParseExactTime(string inputDate)
        {
            try
            {
                if (DateTime.TryParse(inputDate, out DateTime result))
                {
                    return result.ToString("yyyy-MM-dd");
                }
                else
                {
                    throw new Exception("解析日期失败【" + inputDate + "】");
                }

            }
            catch (Exception exc)
            {
                throw;
            }
        }



        private BindingList<IResultContainer> GetContainerItems(string contaienrString)
        {
            try
            {
                BindingList<IResultContainer> containerItemList = new BindingList<IResultContainer>();
                {
                    IResultContainer containerItem = new IResultContainer();

                    containerItemList.Add(containerItem);

                }

                return containerItemList;
            }
            catch (Exception exc)
            {
                throw;
            }
        }


    }
}
