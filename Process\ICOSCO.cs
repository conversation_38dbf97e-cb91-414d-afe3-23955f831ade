﻿using HIH.Framework.AutoCrawingManager.Result;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager.Process
{
    public class ICOSCO : IProcess
    {
        private readonly string SearchMainPage = "https://elines.coscoshipping.com/scct/public/ct/base?lang=zh&trackingType=BILLOFLADING&number=";

        public ICOSCO():base("COSCO","COSU"){}


        public override List<IProcessItem> Run(string searchKey,string replaceText)
        {
            List<IProcessItem> processList = new List<IProcessItem>();

            IProcessItem firPro = new IProcessItem(0, "跳转", IProcessType.JUMPTO);
            IProcessItem secPro = new IProcessItem(4, "解析", IProcessType.READ, this.GetResult);

            searchKey = string.IsNullOrEmpty(replaceText) ? searchKey : searchKey.Replace(replaceText, "");
            firPro.JScript = this.SearchMainPage + searchKey;
            secPro.JScript = "document.documentElement.innerHTML";

            processList.Add(firPro);
            processList.Add(secPro);

            return processList;
        }

        private IResult GetResult(string resultString)
        {
            try
            {
                IResult result = new IResult();
                try
                {
                    result.ETA = this.GetETA(resultString);
                }catch(Exception exc)
                {
                    result.ETAExc = exc.Message;
                }
                try
                {
                    result.ContainerList = this.GetContainerItems(resultString);
                }
                catch (Exception exc)
                {
                    result.ContainerExc = exc.Message;
                }
                return result;
            }
            catch(Exception exc)
            {
                throw;
            }
        }


        private BindingList<IResultContainer> GetContainerItems(string containerString)
        {
            try
            {
                BindingList<IResultContainer> containerList = new BindingList<IResultContainer>();

                string trPattern = @"<tr[^>]*class=""ant-table-row ant-table-row-level-0""[^>]*>(.*?)</tr>";

                MatchCollection containerMatches = Regex.Matches(containerString, trPattern, RegexOptions.Singleline);

                foreach (Match containerMatch in containerMatches)
                {
                    string tdContainerString = containerMatch.Value;
                    string tdPattern = @"<td[^>]*class=""ant-table-cell[^>]*>(.*?)<\/td>";
                    MatchCollection tdMatches = Regex.Matches(tdContainerString, tdPattern, RegexOptions.Singleline);
                    string containerNo = "";
                    string returnDateString = "";
                    string pickupDateString = "";
                    string uploadDateString = "";
                    foreach (Match tdMatch in tdMatches)
                    {
                        string td = tdMatch.Groups[1].Value.Replace("<!---->", "");
                        if (td.Contains("div"))
                        {
                            string divPattern = @"<div[^>]*>(.*?)<\/div>";
                            MatchCollection divMatches = Regex.Matches(td, divPattern, RegexOptions.Singleline);


                            if (divMatches.Count == 1)
                            {
                                containerNo = divMatches[0].Groups[1].Value.Trim();
                            }
                            if (divMatches.Count == 2)
                            {
                                // 获取状态文本，去除可能的空白字符
                                string statusText = divMatches[0].Groups[1].Value.Trim();
                                string dateText = divMatches[1].Groups[1].Value.Trim();


                                if (statusText == "还空箱" || statusText == "Empty Return")
                                {
                                    returnDateString = dateText.Replace("At", "").Replace("于", "").Trim();
                                }
                                else if (statusText == "提取重箱" || statusText == "Laden Pick up")
                                {
                                    pickupDateString = dateText.Replace("At", "").Replace("于", "").Trim();
                                }
                                else if (statusText == "目的港卸货" || statusText.StartsWith("Discharged") || statusText == "中转港卸货")
                                {
                                    uploadDateString = dateText.Replace("At", "").Replace("于", "").Trim();
                                }
                            }
                        }
                    }
                    if (!string.IsNullOrEmpty(containerNo))
                    {
                        IResultContainer containerItem = new IResultContainer();
                        Console.WriteLine(containerNo + "-" + pickupDateString + "-" + returnDateString);
                        containerItem.SetNewContainerItem(containerNo, pickupDateString, uploadDateString, returnDateString);
                        containerList.Add(containerItem);
                    }

                }
                return containerList;
            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private string GetETA(string result)
        {
            try
            {
                // 使用字符串查找方法来提取完整的ant-row内容
                List<string> rowContents = ExtractCompleteRowContents(result);
                if (rowContents.Count <= 0)
                    throw new Exception("未找到时间信息行");

                string estimatedArrivalDates = "";
                string actualArrivalDates = "";

                int rowIndex = 0;
                foreach (string rowContent in rowContents)
                {
                    // 查找列信息 - 使用字符串查找方法
                    List<string> colContents = ExtractCompleteColContents(rowContent);


                    for (int i = 0; i < colContents.Count; i++)
                    {
                        string colContent = colContents[i];
                        // 检查是否包含"预计到港"
                        if (colContent.Contains("预计到港"))
                        {
                            // 查找对应的时间信息
                            string datePattern = @"<span[^>]*class=""date""[^>]*>(.*?)</span>";
                            Match dateMatch = Regex.Match(colContent, datePattern, RegexOptions.Singleline);
                            if (dateMatch.Success)
                            {
                                string dateValue = dateMatch.Groups[1].Value.Trim();
                                Console.WriteLine($"预计到港日期值: {dateValue}");
                                if (!string.IsNullOrEmpty(dateValue) && DateTime.TryParse(dateValue, out DateTime parsedDate))
                                {
                                    estimatedArrivalDates = dateValue;
                                }
                            }
                        }
                        // 检查是否包含"实际到港"
                        else if (colContent.Contains("实际到港"))
                        {
                            // 查找对应的时间信息
                            string datePattern = @"<span[^>]*class=""date""[^>]*>(.*?)</span>";
                            Match dateMatch = Regex.Match(colContent, datePattern, RegexOptions.Singleline);
                            if (dateMatch.Success)
                            {
                                string dateValue = dateMatch.Groups[1].Value.Trim();
                                if (!string.IsNullOrEmpty(dateValue) && DateTime.TryParse(dateValue, out DateTime parsedDate))
                                {
                                    actualArrivalDates = dateValue;
                                }
                            }
                        }
                    }
                    rowIndex++;
                }


                // 优先返回最后一个预计到港时间
                if (!string.IsNullOrEmpty(estimatedArrivalDates))
                {
                    if (DateTime.TryParse(estimatedArrivalDates, out DateTime etaDate))
                    {
                        return etaDate.ToString("yyyy-MM-dd");
                    }
                }

                // 如果没有预计到港，返回最后一个实际到港时间
                if (!string.IsNullOrEmpty(actualArrivalDates))
                {
                    if (DateTime.TryParse(actualArrivalDates, out DateTime ataDate))
                    {
                        return ataDate.ToString("yyyy-MM-dd");
                    }
                }

                throw new Exception("未找到有效的到港时间信息");
            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private List<string> ExtractCompleteRowContents(string html)
        {
            List<string> rowContents = new List<string>();
            string searchPattern = "ant-row css-1tiubaq";
            int startIndex = 0;

            while (true)
            {
                // 查找下一个ant-row的开始位置
                int rowStart = html.IndexOf(searchPattern, startIndex);
                if (rowStart == -1) break;

                // 回退到<div标签的开始
                int divStart = html.LastIndexOf("<div", rowStart);
                if (divStart == -1) break;

                // 查找对应的结束标签
                int divEnd = FindMatchingDivEnd(html, divStart);
                if (divEnd == -1) break;

                // 提取完整的div内容
                string rowContent = html.Substring(divStart, divEnd - divStart + 6); // +6 for "</div>"

                // 提取div内部的内容（去掉外层div标签）
                int contentStart = html.IndexOf('>', divStart) + 1;
                int contentEnd = html.LastIndexOf("</div>", divEnd);
                if (contentStart < contentEnd)
                {
                    string innerContent = html.Substring(contentStart, contentEnd - contentStart);
                    rowContents.Add(innerContent);
                }

                startIndex = divEnd + 1;
            }

            return rowContents;
        }

        private List<string> ExtractCompleteColContents(string rowHtml)
        {
            List<string> colContents = new List<string>();
            string searchPattern = "ant-col css-1tiubaq";
            int startIndex = 0;

            while (true)
            {
                // 查找下一个ant-col的开始位置
                int colStart = rowHtml.IndexOf(searchPattern, startIndex);
                if (colStart == -1) break;

                // 回退到<div标签的开始
                int divStart = rowHtml.LastIndexOf("<div", colStart);
                if (divStart == -1) break;

                // 查找对应的结束标签
                int divEnd = FindMatchingDivEnd(rowHtml, divStart);
                if (divEnd == -1) break;

                // 提取div内部的内容（去掉外层div标签）
                int contentStart = rowHtml.IndexOf('>', divStart) + 1;
                int contentEnd = rowHtml.LastIndexOf("</div>", divEnd);
                if (contentStart < contentEnd)
                {
                    string innerContent = rowHtml.Substring(contentStart, contentEnd - contentStart);
                    colContents.Add(innerContent);
                }

                startIndex = divEnd + 1;
            }

            return colContents;
        }

        private int FindMatchingDivEnd(string html, int divStartIndex)
        {
            int divCount = 1;
            int currentIndex = html.IndexOf('>', divStartIndex) + 1;

            while (currentIndex < html.Length && divCount > 0)
            {
                int nextDiv = html.IndexOf("<div", currentIndex);
                int nextEndDiv = html.IndexOf("</div>", currentIndex);

                if (nextEndDiv == -1) break;

                if (nextDiv != -1 && nextDiv < nextEndDiv)
                {
                    divCount++;
                    currentIndex = nextDiv + 4;
                }
                else
                {
                    divCount--;
                    if (divCount == 0)
                    {
                        return nextEndDiv;
                    }
                    currentIndex = nextEndDiv + 6;
                }
            }

            return -1;
        }


    }
}
