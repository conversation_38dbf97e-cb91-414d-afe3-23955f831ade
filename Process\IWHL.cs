﻿using HIH.Framework.AutoCrawingManager.Result;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace HIH.Framework.AutoCrawingManager.Process
{
    public class IWHL : IProcess
    {
        private readonly string SearchMainPage = "https://www.wanhai.com/views/Main.xhtml";

        public IWHL():base("WHL","WHLC"){}


        public override List<IProcessItem> Run(string searchKey,string replaceText)
        {
            List<IProcessItem> processList = new List<IProcessItem>();

            IProcessItem firPro = new IProcessItem(0, "跳转", IProcessType.JUMPTO);
            IProcessItem secPro = new IProcessItem(1, "输入", IProcessType.OPERATE);
            IProcessItem thiPro = new IProcessItem(2, "点击", IProcessType.OPERATE);
            IProcessItem fouPro = new IProcessItem(3, "点击", IProcessType.OPERATE);
            IProcessItem fifPro = new IProcessItem(4, "解析", IProcessType.READ, this.GetResult);

            searchKey = string.IsNullOrEmpty(replaceText) ? searchKey : searchKey.Replace(replaceText, "");
            firPro.JScript = this.SearchMainPage;
            secPro.JScript = "document.getElementById('q_ref_no1').value='" + searchKey + "';";
            thiPro.JScript = "document.getElementById('quick_ctnr_query').click();";
            fouPro.JScript = "document.getElementById('customerI').click();";
            fifPro.JScript = "document.documentElement.innerHTML";

            processList.Add(firPro);
            processList.Add(secPro);
            processList.Add(thiPro);
            processList.Add(fouPro);
            processList.Add(fifPro);

            return processList;
        }

        private IResult GetResult(string resultString)
        {
            try
            {
                IResult result = new IResult();
                try
                {
                    result.ETA = this.GetETA(resultString);
                }
                catch (Exception exc)
                {
                    result.ETAExc = exc.Message;
                }
                return result;
            }
            catch (Exception exc)
            {
                throw;
            }
        }



        private string GetETA(string result)
        {
            try
            {

                string tablePattern = @"<table[^>]*>.*?</table>";

                MatchCollection matches = Regex.Matches(result, tablePattern, RegexOptions.Singleline);

                string matchVal = "";

                foreach (Match match in matches)
                {
                    if (match.Value.Contains("Place of Receipt"))
                    {
                        matchVal = match.Value;
                        break;
                    }

                }
                if (string.IsNullOrEmpty(matchVal))
                    throw new Exception("未找到正确的匹配对象");

                string trPattern = @"<tr[^>]*>.*?</tr>";
                matches = Regex.Matches(matchVal, trPattern, RegexOptions.Singleline);
                List<Dictionary<string, List<string>>> tbList = new List<Dictionary<string, List<string>>>();

                bool isHaveSec = false;
                int index = 1;
                foreach (Match match in matches)
                {
                    string thtdPattern = @"<(th|td) [^>]*>(.*?)<\/\1>";
                    Regex regex = new Regex(thtdPattern, RegexOptions.IgnoreCase);
                    MatchCollection thtdMatches = regex.Matches(match.Value);

                    string currTitText = "";
                    Dictionary<string, List<string>> rowDic = new Dictionary<string, List<string>>();
                    //人为创建一个th
                    if (isHaveSec)
                    {
                        currTitText = "T/S Port Second" + "_" + index;
                        rowDic.Add(currTitText, new List<string>());
                        isHaveSec = false;
                        index++;
                    }

                    foreach (Match thtdMatch in thtdMatches)
                    {

                        if (thtdMatch.Value.Contains("th"))
                        {
                            string thPattern = @"<th[^>]*>(?:<[^>]+>)?(.*?)(?:<\/[^>]+>)?<\/th>";
                            Match thMatch = Regex.Match(thtdMatch.Value, thPattern);
                            if (thMatch.Success)
                            {
                                currTitText = thMatch.Groups[1].Value;
                                if (currTitText == "T/S Port")
                                {
                                    isHaveSec = true;
                                    currTitText = currTitText + " First_" + index;
                                }
                                rowDic.Add(currTitText, new List<string>());
                            }
                        }
                        if (!string.IsNullOrEmpty(currTitText) && thtdMatch.Value.Contains("td"))
                        {
                            string tdPattern = @"<td[^>]*>(?:<[^>]+>)?(.*?)(?:<\/[^>]+>)?<\/td>";
                            Match tdMatch = Regex.Match(thtdMatch.Value, tdPattern);
                            if (tdMatch.Success && rowDic.ContainsKey(currTitText))
                            {
                                rowDic[currTitText].Add(tdMatch.Groups[1].Value
                                    .Replace("<!--", "")
                                    .Replace("-->", "")
                                    .Replace("\n", "")
                                    .Replace("\t", "")
                                    .Replace("special control start", "")
                                    .Replace("special control end", "")
                                    .Replace("t ", "").Trim());
                            }
                        }

                    }
                    tbList.Add(rowDic);
                }
                return this.SetPortResultItem(tbList);
            }
            catch (Exception exc)
            {
                throw;
            }
        }

        private string SetPortResultItem(List<Dictionary<string, List<string>>> tbList)
        {
            string catchDcEta = "无";
            try
            {
                foreach (Dictionary<string, List<string>> rowDic in tbList)
                {


                    if (rowDic.ContainsKey("Place of Delivery"))
                    {
                        foreach (var rowDicItem in rowDic)
                        {
                            if (rowDicItem.Key.StartsWith("Estimated Arrival Date") &&
                                DateTime.TryParse(rowDicItem.Value.Count > 0 ? rowDicItem.Value[0] : "", out DateTime dat))
                            {
                                catchDcEta = dat.ToString("yyyy-MM-dd");
                            }
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw;
            }
            return catchDcEta;
        }





    }
}
